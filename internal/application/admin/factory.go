package admin

import (
	kitlog "github.com/go-kit/log"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/terminal_service/internal/config"
	"github.com/liveutil/terminal_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/terminal_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
)

type TerminalAdminServiceOpts struct {
	Repository      postgres.Store
	Config          *config.Configuration
	Redis           *redis.Client
	NATS            *nats.Conn
	Logger          kitlog.Logger
	PASETO          paseto.Maker
	SchemaPath      string
	ApplicationName string
}

// NewTerminalAdminService creates a new terminal administration service with all middleware layers
func NewTerminalAdminService(opts *TerminalAdminServiceOpts) (pb.TerminalAdminServiceServer, error) {
	// Create base service
	svc := NewService(opts)

	// Add middleware layers
	svc = NewAuthorizationMiddleware(svc)

	return svc, nil
}
