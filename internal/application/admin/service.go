package admin

import (
	"context"

	kitlog "github.com/go-kit/log"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/totp"
	"github.com/liveutil/terminal_service/internal/config"
	"github.com/liveutil/terminal_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/terminal_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/pquerna/otp"
	"github.com/redis/go-redis/v9"
)

type service struct {
	pb.UnimplementedTerminalAdminServiceServer
	repo         postgres.Store
	paseto       paseto.Maker
	config       *config.Configuration
	redis        *redis.Client
	nats         *nats.Conn
	logger       kitlog.Logger
	totp_config  totp.TOTPServiceConfig
	totp_service totp.TOTPService
}

func NewService(opts *TerminalAdminServiceOpts) pb.TerminalAdminServiceServer {
	totpConfig := totp.TOTPServiceConfig{
		Issuer:    opts.Config.Issuer,
		Digits:    6,
		Period:    300,
		Algorithm: otp.AlgorithmSHA1,
		Skew:      1,
	}

	totpService := totp.NewRedisTOTPService(opts.Redis, opts.ApplicationName, totpConfig)

	return &service{
		repo:         opts.Repository,
		config:       opts.Config,
		redis:        opts.Redis,
		paseto:       opts.PASETO,
		nats:         opts.NATS,
		totp_config:  totpConfig,
		totp_service: totpService,
		logger:       opts.Logger,
	}
}

// CreateTerminal implements pb.TerminalAdminServiceServer.
func (s *service) CreateTerminal(context.Context, *pb.CreateTerminalRequest) (*pb.CreateTerminalResponse, error) {
	panic("unimplemented")
}

// DeleteTerminal implements pb.TerminalAdminServiceServer.
func (s *service) DeleteTerminal(context.Context, *pb.DeleteTerminalRequest) (*pb.DeleteTerminalResponse, error) {
	panic("unimplemented")
}

// GetTerminal implements pb.TerminalAdminServiceServer.
func (s *service) GetTerminal(context.Context, *pb.GetTerminalRequest) (*pb.GetTerminalResponse, error) {
	panic("unimplemented")
}

// ListTerminals implements pb.TerminalAdminServiceServer.
func (s *service) ListTerminals(context.Context, *pb.ListTerminalsRequest) (*pb.ListTerminalsResponse, error) {
	panic("unimplemented")
}

// UpdateTerminal implements pb.TerminalAdminServiceServer.
func (s *service) UpdateTerminal(context.Context, *pb.UpdateTerminalRequest) (*pb.UpdateTerminalResponse, error) {
	panic("unimplemented")
}
