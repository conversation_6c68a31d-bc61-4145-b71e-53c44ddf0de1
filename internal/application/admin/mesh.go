package admin

import (
	"github.com/liveutil/terminal_service/internal/infrastructure/db/postgres"
	"github.com/nats-io/nats.go"
)

// MeshService implements servicemesh.UserMeshService.
type terminalAdminMeshService struct {
	repo          postgres.Store
	nats          *nats.Conn
	app           string
	subscriptions map[string]*nats.Subscription
}

// NewMeshService creates a new user mesh service.
// func NewMeshService(opts *TerminalServiceOpts) servicemesh.UserMeshService {
// 	return &terminalMeshService{
// 		repo:          opts.Repository,
// 		nats:          opts.NATS,
// 		app:           opts.ApplicationName,
// 		subscriptions: make(map[string]*nats.Subscription),
// 	}
// }

// Start implements servicemesh.UserMeshService.
// func (u *terminalMeshService) Start(ctx context.Context, subjects []string) error {
// 	for _, subject := range subjects {
// 		sub, err := u.nats.QueueSubscribe(subject, u.app, func(msg *nats.Msg) {
// 			fmt.Println("mesh service message arrived", string(msg.Data))
// 			message := &servicemesh.UserMeshServiceMessage{}
// 			if err := json.Unmarshal(msg.Data, message); err != nil {
// 				msg.Respond([]byte("error: invalid message format"))
// 				return
// 			}

// 			switch msg.Subject {
// 			case servicemesh.USER_SERVICE_GET_SAFE_USER_BY_ID:
// 				if message.UserID == 0 {
// 					data, err := json.Marshal(servicemesh.UserModel{})
// 					if err != nil {
// 						msg.Respond([]byte("error: failed to marshal empty user info"))
// 						return
// 					}
// 					msg.Respond(data)
// 				}

// 				user, err := u.GetSafeUserByID(ctx, message.UserID)
// 				if err != nil {
// 					data, err := json.Marshal(servicemesh.UserModel{})
// 					if err != nil {
// 						msg.Respond([]byte("error: failed to marshal empty user info"))
// 						return
// 					}
// 					msg.Respond(data)
// 				}
// 				data, err := json.Marshal(user)
// 				if err != nil {
// 					msg.Respond([]byte("error: failed to marshal user info"))
// 					return
// 				}
// 				if err := msg.Respond(data); err != nil {
// 					return
// 				}
// 			case servicemesh.USER_SERVICE_GET_SAFE_USER_BY_IDENTIFIER:
// 				// Handle GetSafeUserByIdentifier
// 				if message.Identifier == "" {
// 					data, err := json.Marshal(servicemesh.UserModel{})
// 					if err != nil {
// 						msg.Respond([]byte("error: failed to marshal empty user info"))
// 						return
// 					}
// 					msg.Respond(data)
// 				}

// 				user, err := u.GetSafeUserByIdentifier(ctx, message.Identifier)
// 				if err != nil {
// 					data, err := json.Marshal(servicemesh.UserModel{})
// 					if err != nil {
// 						msg.Respond([]byte("error: failed to marshal empty user info"))
// 						return
// 					}
// 					msg.Respond(data)
// 				}

// 				data, err := json.Marshal(user)
// 				if err != nil {
// 					msg.Respond([]byte("error: failed to marshal user info"))
// 					return
// 				}
// 				if err := msg.Respond(data); err != nil {
// 					return
// 				}
// 			}
// 		})

// 		if err != nil {
// 			return err
// 		}

// 		u.subscriptions[subject] = sub
// 	}
// 	return nil
// }

// Stop implements servicemesh.UserMeshService.
// func (u *terminalMeshService) Stop(ctx context.Context) error {
// 	for subject, sub := range u.subscriptions {
// 		if err := sub.Unsubscribe(); err != nil {
// 			return err
// 		}
// 		delete(u.subscriptions, subject)
// 	}
// 	return nil
// }
