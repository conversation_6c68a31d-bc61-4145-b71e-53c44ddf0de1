package terminal

import (
	kitlog "github.com/go-kit/log"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/terminal_service/internal/config"
	"github.com/liveutil/terminal_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/terminal_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
)

type TerminalServiceOpts struct {
	Repository      postgres.Store
	Config          *config.Configuration
	Redis           *redis.Client
	NATS            *nats.Conn
	Logger          kitlog.Logger
	PASETO          paseto.Maker
	SchemaPath      string
	ApplicationName string
}

// NewTerminalService creates a new terminal service with all middleware layers
func NewTerminalService(opts *TerminalServiceOpts) (pb.TerminalServiceServer, error) {
	// Create base service
	svc := NewService(opts)

	// Add middleware layers
	svc = NewAuthorizationMiddleware(svc)

	return svc, nil
}
