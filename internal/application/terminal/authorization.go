package terminal

import (
	"github.com/liveutil/terminal_service/pb"
)

type authorizationMiddleware struct {
	pb.UnimplementedTerminalServiceServer

	next pb.TerminalServiceServer
}

// NewAuthorizationMiddleware returns new authorization layer for pb.TerminalServiceServer
func NewAuthorizationMiddleware(service pb.TerminalServiceServer) pb.TerminalServiceServer {
	return &authorizationMiddleware{
		next: service,
	}
}
