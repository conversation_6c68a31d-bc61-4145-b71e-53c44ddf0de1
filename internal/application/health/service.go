package health

import (
	"context"
	"time"

	"github.com/liveutil/terminal_service/internal/domain"
	"github.com/rs/zerolog/log"
)

type service struct {
	repo domain.HealthRepository
}

// NewHealthCheckService creates a new health service instance
func NewHealthCheckService(repo domain.HealthRepository) domain.HealthService {
	return &service{
		repo: repo,
	}
}

// Check performs a health check of all components
func (s *service) Check(ctx context.Context) (*domain.HealthStatus, error) {
	status := &domain.HealthStatus{
		Status:    "healthy",
		Timestamp: time.Now(),
	}

	// Check database
	if err := s.repo.CheckDatabase(ctx); err != nil {
		status.Status = "unhealthy"
		status.Details.Database = err.Error()
		log.Error().Err(err).Msg("Database health check failed")
	} else {
		status.Details.Database = "healthy"
	}

	// Check Redis
	if err := s.repo.CheckRedis(ctx); err != nil {
		status.Status = "unhealthy"
		status.Details.Redis = err.Error()
		log.Error().Err(err).Msg("Redis health check failed")
	} else {
		status.Details.Redis = "healthy"
	}

	// Check MongoDB
	if err := s.repo.CheckMongoDB(ctx); err != nil {
		status.Status = "unhealthy"
		status.Details.MongoDB = err.Error()
		log.Error().Err(err).Msg("MongoDB health check failed")
	} else {
		status.Details.MongoDB = "healthy"
	}

	// Check Message Bus
	if err := s.repo.CheckMessageBus(ctx); err != nil {
		status.Status = "unhealthy"
		status.Details.MessageBus = err.Error()
		log.Error().Err(err).Msg("Message Bus health check failed")
	} else {
		status.Details.MessageBus = "healthy"
	}

	// Check Service Mesh
	if err := s.repo.CheckServiceMesh(ctx); err != nil {
		status.Status = "unhealthy"
		status.Details.ServiceMesh = err.Error()
		log.Error().Err(err).Msg("Service Mesh health check failed")
	} else {
		status.Details.ServiceMesh = "healthy"
	}

	return status, nil
}

// Watch starts a health check watcher that periodically checks the health status
func (s *service) Watch(ctx context.Context) (<-chan *domain.HealthStatus, error) {
	statusChan := make(chan *domain.HealthStatus)
	ticker := time.NewTicker(30 * time.Second)

	go func() {
		defer close(statusChan)
		defer ticker.Stop()

		// Send initial health check
		if status, err := s.Check(ctx); err == nil {
			statusChan <- status
		}

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if status, err := s.Check(ctx); err == nil {
					select {
					case statusChan <- status:
					case <-ctx.Done():
						return
					}
				}
			}
		}
	}()

	return statusChan, nil
}
