package health

import (
	"github.com/liveutil/terminal_service/internal/domain"
	healthRepo "github.com/liveutil/terminal_service/internal/infrastructure/health"
)

// NewHealthService creates a new health service with all middleware layers
func NewHealthService(opts *HealthServiceOpts) domain.HealthService {
	// Create repository
	repo := healthRepo.NewRepository(opts.Database, opts.Redis, opts.Mongo, opts.NATS, opts.DaprClient)

	// Create base service
	svc := NewHealthCheckService(repo)

	return svc
}
