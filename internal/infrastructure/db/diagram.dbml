Project terminal_service {
  database_type: 'PostgreSQL'
  Note: '''
    # POS Terminal Device Management Service Database
  '''
}

// terminals table contains SMD for POS Terminal Devices.
Table terminals {
  id bigserial [pk, note: 'terminal unique id']
  identifier varchar(64) [unique, not null, note: 'unique external identifier for inter system internal-external identifier separation']
  approved boolean [not null, default: false, note: 'is terminal approved or no']
  banned boolean [not null, default: false, note: 'is terminal banned or no']

  meta_data jsonb [not null, default: '{}', note: 'terminal metadatas']
  
  allowed_accounts text[] [not null, default: '{}', note: 'POS Device Terminal allowed destination accounts for requesting payment transaction']
  tls_key text [not null, note: 'POS Device Terminal TLS Key']
  app_version text [not null, note: 'POS Device Terminal App Version']
  firmware_version text [not null, note: 'POS Device Terminal Firmware Version']
  firmware_hash text [not null, note: 'POS Device Terminal Firmware Hash']
  allowed_domains text[] [not null, default: '{}', note: 'POS Device Terminal allowed destination domains for requesting payment transaction']
  allowed_assets text[] [not null, default: '{}', note: 'POS Device Terminal allowed destination assets for requesting payment transaction']
  device_id varchar(256) [not null, unique, note: 'POS Device Terminal Unique Device ID']
  
  expires_at timestamptz [note: 'expire time of terminal, if not sets then terminal valid for unlimited time']
  created_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when terminal was created']
  updated_at timestamptz  [note: 'when terminal was updated']
  deleted_at timestamptz [note: 'when terminal was deleted']

  Indexes {
    id
    identifier  
    deleted_at
    (id, identifier, deleted_at)
    (id, identifier, banned, approved, deleted_at)
    (banned, approved)
  }
}