-- SQL dump generated using DBML (dbml.dbdiagram.io)
-- Database: PostgreSQL
-- Generated at: 2025-07-30T05:46:49.381Z

CREATE TABLE "terminals" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "approved" boolean NOT NULL DEFAULT false,
  "banned" boolean NOT NULL DEFAULT false,
  "meta_data" jsonb NOT NULL DEFAULT '{}',
  "allowed_accounts" text[] NOT NULL DEFAULT '{}',
  "tls_key" text NOT NULL,
  "app_version" text NOT NULL,
  "firmware_version" text NOT NULL,
  "firmware_hash" text NOT NULL,
  "allowed_domains" text[] NOT NULL DEFAULT '{}',
  "allowed_assets" text[] NOT NULL DEFAULT '{}',
  "device_id" varchar(256) UNIQUE NOT NULL,
  "expires_at" timestamptz,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE INDEX ON "terminals" ("id");

CREATE INDEX ON "terminals" ("identifier");

CREATE INDEX ON "terminals" ("deleted_at");

CREATE INDEX ON "terminals" ("id", "identifier", "deleted_at");

CREATE INDEX ON "terminals" ("id", "identifier", "banned", "approved", "deleted_at");

CREATE INDEX ON "terminals" ("banned", "approved");

COMMENT ON COLUMN "terminals"."id" IS 'terminal unique id';

COMMENT ON COLUMN "terminals"."identifier" IS 'unique external identifier for inter system internal-external identifier separation';

COMMENT ON COLUMN "terminals"."approved" IS 'is terminal approved or no';

COMMENT ON COLUMN "terminals"."banned" IS 'is terminal banned or no';

COMMENT ON COLUMN "terminals"."meta_data" IS 'terminal metadatas';

COMMENT ON COLUMN "terminals"."allowed_accounts" IS 'POS Device Terminal allowed destination accounts for requesting payment transaction';

COMMENT ON COLUMN "terminals"."tls_key" IS 'POS Device Terminal TLS Key';

COMMENT ON COLUMN "terminals"."app_version" IS 'POS Device Terminal App Version';

COMMENT ON COLUMN "terminals"."firmware_version" IS 'POS Device Terminal Firmware Version';

COMMENT ON COLUMN "terminals"."firmware_hash" IS 'POS Device Terminal Firmware Hash';

COMMENT ON COLUMN "terminals"."allowed_domains" IS 'POS Device Terminal allowed destination domains for requesting payment transaction';

COMMENT ON COLUMN "terminals"."allowed_assets" IS 'POS Device Terminal allowed destination assets for requesting payment transaction';

COMMENT ON COLUMN "terminals"."device_id" IS 'POS Device Terminal Unique Device ID';

COMMENT ON COLUMN "terminals"."expires_at" IS 'expire time of terminal, if not sets then terminal valid for unlimited time';

COMMENT ON COLUMN "terminals"."created_at" IS 'when terminal was created';

COMMENT ON COLUMN "terminals"."updated_at" IS 'when terminal was updated';

COMMENT ON COLUMN "terminals"."deleted_at" IS 'when terminal was deleted';