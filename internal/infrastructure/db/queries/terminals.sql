-- name: GetTerminalById :one
SELECT *
FROM terminals
WHERE id = $1
LIMIT 1;

-- name: GetTerminalByDeviceId :one
SELECT *
FROM terminals
WHERE device_id = $1
LIMIT 1;

-- name: GetTerminalByIdentifier :one
SELECT *
FROM terminals
WHERE identifier = $1
LIMIT 1;

-- name: CreateTerminal :one
INSERT INTO terminals (
  identifier,
  approved,
  banned,
  meta_data,
  allowed_accounts,
  tls_key,
  app_version,
  firmware_version,
  firmware_hash,
  allowed_domains,
  allowed_assets,
  device_id,
  expires_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING *;

-- name: UpdateTerminal :one
UPDATE terminals
SET
  approved = $2,
  banned = $3,
  meta_data = $4,
  allowed_accounts = $5,
  tls_key = $6,
  app_version = $7,
  firmware_version = $8,
  firmware_hash = $9,
  allowed_domains = $10,
  allowed_assets = $11,
  device_id = $12,
  expires_at = $13
WHERE id = $1
RETURNING *;

-- name: SoftDeleteTerminal :one
UPDATE terminals
SET
  deleted_at = $2
WHERE id = $1
RETURNING *;

