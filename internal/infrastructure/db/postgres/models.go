// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type Terminal struct {
	// terminal unique id
	ID int64 `json:"id"`
	// unique external identifier for inter system internal-external identifier separation
	Identifier string `json:"identifier"`
	// is terminal approved or no
	Approved bool `json:"approved"`
	// is terminal banned or no
	Banned bool `json:"banned"`
	// terminal metadatas
	MetaData []byte `json:"meta_data"`
	// POS Device Terminal allowed destination accounts for requesting payment transaction
	AllowedAccounts []string `json:"allowed_accounts"`
	// POS Device Terminal TLS Key
	TlsKey string `json:"tls_key"`
	// POS Device Terminal App Version
	AppVersion string `json:"app_version"`
	// POS Device Terminal Firmware Version
	FirmwareVersion string `json:"firmware_version"`
	// POS Device Terminal Firmware Hash
	FirmwareHash string `json:"firmware_hash"`
	// POS Device Terminal allowed destination domains for requesting payment transaction
	AllowedDomains []string `json:"allowed_domains"`
	// POS Device Terminal allowed destination assets for requesting payment transaction
	AllowedAssets []string `json:"allowed_assets"`
	// POS Device Terminal Unique Device ID
	DeviceID string `json:"device_id"`
	// expire time of terminal, if not sets then terminal valid for unlimited time
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when terminal was created
	CreatedAt time.Time `json:"created_at"`
	// when terminal was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when terminal was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}
