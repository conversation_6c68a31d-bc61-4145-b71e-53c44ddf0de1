// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"context"
)

type Querier interface {
	CreateTerminal(ctx context.Context, arg CreateTerminalParams) (Terminal, error)
	GetTerminalByDeviceId(ctx context.Context, deviceID string) (Terminal, error)
	GetTerminalById(ctx context.Context, id int64) (Terminal, error)
	GetTerminalByIdentifier(ctx context.Context, identifier string) (Terminal, error)
	SoftDeleteTerminal(ctx context.Context, arg SoftDeleteTerminalParams) (Terminal, error)
	UpdateTerminal(ctx context.Context, arg UpdateTerminalParams) (Terminal, error)
}

var _ Querier = (*Queries)(nil)
