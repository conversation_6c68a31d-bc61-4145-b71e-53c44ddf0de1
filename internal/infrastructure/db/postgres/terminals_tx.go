package postgres

import "context"

// CreateUserTxParams is the params for CreateUserTx.
type CreateTerminalTxParams struct {
	CreateTerminalParams
	AfterCreate func(user Terminal) error
}

// UserTxResult is the result for CreateUserTx.
type CreateTerminalTxResult struct {
	Terminal Terminal
}

// CreateTerminalTx implements Store.CreateTerminalTx
func (store *SQLStore) CreateTerminalTx(ctx context.Context, arg CreateTerminalTxParams) (CreateTerminalTxResult, error) {
	var result CreateTerminalTxResult

	err := store.execTx(ctx, func(q *Queries) error {
		var err error

		result.Terminal, err = q.CreateTerminal(ctx, arg.CreateTerminalParams)
		if err != nil {
			return err
		}

		return arg.AfterCreate(result.Terminal)
	})

	return result, err
}
