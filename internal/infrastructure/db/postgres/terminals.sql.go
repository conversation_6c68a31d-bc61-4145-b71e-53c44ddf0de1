// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: terminals.sql

package postgres

import (
	"context"
)

const getTerminalById = `-- name: GetTerminalById :one
SELECT id, identifier, approved, banned, meta_data, allowed_accounts, tls_key, app_version, firmware_version, firmware_hash, allowed_domains, allowed_assets, device_id, expires_at, created_at, updated_at, deleted_at
FROM terminals
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetTerminalById(ctx context.Context, id int64) (Terminal, error) {
	row := q.db.QueryRow(ctx, getTerminalById, id)
	var i Terminal
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.AllowedAccounts,
		&i.TlsKey,
		&i.AppVersion,
		&i.FirmwareVersion,
		&i.FirmwareHash,
		&i.AllowedDomains,
		&i.AllowedAssets,
		&i.DeviceID,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
