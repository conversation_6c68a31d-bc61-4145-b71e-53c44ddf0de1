// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: terminals.sql

package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createTerminal = `-- name: CreateTerminal :one
INSERT INTO terminals (
  identifier,
  approved,
  banned,
  meta_data,
  allowed_accounts,
  tls_key,
  app_version,
  firmware_version,
  firmware_hash,
  allowed_domains,
  allowed_assets,
  device_id,
  expires_at
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id, identifier, approved, banned, meta_data, allowed_accounts, tls_key, app_version, firmware_version, firmware_hash, allowed_domains, allowed_assets, device_id, expires_at, created_at, updated_at, deleted_at
`

type CreateTerminalParams struct {
	Identifier      string             `json:"identifier"`
	Approved        bool               `json:"approved"`
	Banned          bool               `json:"banned"`
	MetaData        []byte             `json:"meta_data"`
	AllowedAccounts []string           `json:"allowed_accounts"`
	TlsKey          string             `json:"tls_key"`
	AppVersion      string             `json:"app_version"`
	FirmwareVersion string             `json:"firmware_version"`
	FirmwareHash    string             `json:"firmware_hash"`
	AllowedDomains  []string           `json:"allowed_domains"`
	AllowedAssets   []string           `json:"allowed_assets"`
	DeviceID        string             `json:"device_id"`
	ExpiresAt       pgtype.Timestamptz `json:"expires_at"`
}

func (q *Queries) CreateTerminal(ctx context.Context, arg CreateTerminalParams) (Terminal, error) {
	row := q.db.QueryRow(ctx, createTerminal,
		arg.Identifier,
		arg.Approved,
		arg.Banned,
		arg.MetaData,
		arg.AllowedAccounts,
		arg.TlsKey,
		arg.AppVersion,
		arg.FirmwareVersion,
		arg.FirmwareHash,
		arg.AllowedDomains,
		arg.AllowedAssets,
		arg.DeviceID,
		arg.ExpiresAt,
	)
	var i Terminal
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.AllowedAccounts,
		&i.TlsKey,
		&i.AppVersion,
		&i.FirmwareVersion,
		&i.FirmwareHash,
		&i.AllowedDomains,
		&i.AllowedAssets,
		&i.DeviceID,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getTerminalByDeviceId = `-- name: GetTerminalByDeviceId :one
SELECT id, identifier, approved, banned, meta_data, allowed_accounts, tls_key, app_version, firmware_version, firmware_hash, allowed_domains, allowed_assets, device_id, expires_at, created_at, updated_at, deleted_at
FROM terminals
WHERE device_id = $1
LIMIT 1
`

func (q *Queries) GetTerminalByDeviceId(ctx context.Context, deviceID string) (Terminal, error) {
	row := q.db.QueryRow(ctx, getTerminalByDeviceId, deviceID)
	var i Terminal
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.AllowedAccounts,
		&i.TlsKey,
		&i.AppVersion,
		&i.FirmwareVersion,
		&i.FirmwareHash,
		&i.AllowedDomains,
		&i.AllowedAssets,
		&i.DeviceID,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getTerminalById = `-- name: GetTerminalById :one
SELECT id, identifier, approved, banned, meta_data, allowed_accounts, tls_key, app_version, firmware_version, firmware_hash, allowed_domains, allowed_assets, device_id, expires_at, created_at, updated_at, deleted_at
FROM terminals
WHERE id = $1
LIMIT 1
`

func (q *Queries) GetTerminalById(ctx context.Context, id int64) (Terminal, error) {
	row := q.db.QueryRow(ctx, getTerminalById, id)
	var i Terminal
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.AllowedAccounts,
		&i.TlsKey,
		&i.AppVersion,
		&i.FirmwareVersion,
		&i.FirmwareHash,
		&i.AllowedDomains,
		&i.AllowedAssets,
		&i.DeviceID,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getTerminalByIdentifier = `-- name: GetTerminalByIdentifier :one
SELECT id, identifier, approved, banned, meta_data, allowed_accounts, tls_key, app_version, firmware_version, firmware_hash, allowed_domains, allowed_assets, device_id, expires_at, created_at, updated_at, deleted_at
FROM terminals
WHERE identifier = $1
LIMIT 1
`

func (q *Queries) GetTerminalByIdentifier(ctx context.Context, identifier string) (Terminal, error) {
	row := q.db.QueryRow(ctx, getTerminalByIdentifier, identifier)
	var i Terminal
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.AllowedAccounts,
		&i.TlsKey,
		&i.AppVersion,
		&i.FirmwareVersion,
		&i.FirmwareHash,
		&i.AllowedDomains,
		&i.AllowedAssets,
		&i.DeviceID,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const softDeleteTerminal = `-- name: SoftDeleteTerminal :one
UPDATE terminals
SET
  deleted_at = $2
WHERE id = $1
RETURNING id, identifier, approved, banned, meta_data, allowed_accounts, tls_key, app_version, firmware_version, firmware_hash, allowed_domains, allowed_assets, device_id, expires_at, created_at, updated_at, deleted_at
`

type SoftDeleteTerminalParams struct {
	ID        int64              `json:"id"`
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

func (q *Queries) SoftDeleteTerminal(ctx context.Context, arg SoftDeleteTerminalParams) (Terminal, error) {
	row := q.db.QueryRow(ctx, softDeleteTerminal, arg.ID, arg.DeletedAt)
	var i Terminal
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.AllowedAccounts,
		&i.TlsKey,
		&i.AppVersion,
		&i.FirmwareVersion,
		&i.FirmwareHash,
		&i.AllowedDomains,
		&i.AllowedAssets,
		&i.DeviceID,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateTerminal = `-- name: UpdateTerminal :one
UPDATE terminals
SET
  approved = $2,
  banned = $3,
  meta_data = $4,
  allowed_accounts = $5,
  tls_key = $6,
  app_version = $7,
  firmware_version = $8,
  firmware_hash = $9,
  allowed_domains = $10,
  allowed_assets = $11,
  device_id = $12,
  expires_at = $13
WHERE id = $1
RETURNING id, identifier, approved, banned, meta_data, allowed_accounts, tls_key, app_version, firmware_version, firmware_hash, allowed_domains, allowed_assets, device_id, expires_at, created_at, updated_at, deleted_at
`

type UpdateTerminalParams struct {
	ID              int64              `json:"id"`
	Approved        bool               `json:"approved"`
	Banned          bool               `json:"banned"`
	MetaData        []byte             `json:"meta_data"`
	AllowedAccounts []string           `json:"allowed_accounts"`
	TlsKey          string             `json:"tls_key"`
	AppVersion      string             `json:"app_version"`
	FirmwareVersion string             `json:"firmware_version"`
	FirmwareHash    string             `json:"firmware_hash"`
	AllowedDomains  []string           `json:"allowed_domains"`
	AllowedAssets   []string           `json:"allowed_assets"`
	DeviceID        string             `json:"device_id"`
	ExpiresAt       pgtype.Timestamptz `json:"expires_at"`
}

func (q *Queries) UpdateTerminal(ctx context.Context, arg UpdateTerminalParams) (Terminal, error) {
	row := q.db.QueryRow(ctx, updateTerminal,
		arg.ID,
		arg.Approved,
		arg.Banned,
		arg.MetaData,
		arg.AllowedAccounts,
		arg.TlsKey,
		arg.AppVersion,
		arg.FirmwareVersion,
		arg.FirmwareHash,
		arg.AllowedDomains,
		arg.AllowedAssets,
		arg.DeviceID,
		arg.ExpiresAt,
	)
	var i Terminal
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Approved,
		&i.Banned,
		&i.MetaData,
		&i.AllowedAccounts,
		&i.TlsKey,
		&i.AppVersion,
		&i.FirmwareVersion,
		&i.FirmwareHash,
		&i.AllowedDomains,
		&i.AllowedAssets,
		&i.DeviceID,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
