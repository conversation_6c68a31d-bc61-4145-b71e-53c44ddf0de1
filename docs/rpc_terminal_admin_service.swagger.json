{"swagger": "2.0", "info": {"title": "Terminal Service API", "description": "Terminal Service API for authentication and POS device terminal management", "version": "1.0.0", "contact": {"name": "POS Device Terminal Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "TerminalAdminService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/admin/terminals/create": {"post": {"summary": "Create POS Device Terminal", "description": "Create a new POS Device Terminal", "operationId": "TerminalAdminService_CreateTerminal", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCreateTerminalResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "model that contains Create POS Device Terminal request properties.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCreateTerminalRequest"}}], "tags": ["TerminalAdminService"]}}, "/v1/admin/terminals/delete/{terminal_identifier}": {"patch": {"summary": "Delete POS Device Terminal", "description": "Delete an existing POS Device Terminal", "operationId": "TerminalAdminService_DeleteTerminal", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDeleteTerminalResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "terminal_identifier", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TerminalAdminServiceDeleteTerminalBody"}}], "tags": ["TerminalAdminService"]}}, "/v1/admin/terminals/update": {"patch": {"summary": "Update POS Device Terminal", "description": "Update an existing POS Device Terminal", "operationId": "TerminalAdminService_UpdateTerminal", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbUpdateTerminalResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "model that contains Update POS Device Terminal request properties.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUpdateTerminalRequest"}}], "tags": ["TerminalAdminService"]}}, "/v1/admin/terminals/{page_size}/{offset}": {"get": {"summary": "List POS Device Terminals", "description": "List all POS Device Terminals", "operationId": "TerminalAdminService_ListTerminals", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbListTerminalsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page_size", "in": "path", "required": true, "type": "integer", "format": "int32"}, {"name": "offset", "in": "path", "required": true, "type": "integer", "format": "int32"}], "tags": ["TerminalAdminService"]}}, "/v1/admin/terminals/{terminal_identifier}": {"get": {"summary": "Get POS Device Terminal", "description": "Get an existing POS Device Terminal", "operationId": "TerminalAdminService_GetTerminal", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbGetTerminalResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "terminal_identifier", "in": "path", "required": true, "type": "string"}], "tags": ["TerminalAdminService"]}}}, "definitions": {"TerminalAdminServiceDeleteTerminalBody": {"type": "object", "description": "model that contains Delete POS Device Terminal request properties."}, "pbCreateTerminalRequest": {"type": "object", "description": "model that contains Create POS Device Terminal request properties."}, "pbCreateTerminalResponse": {"type": "object", "description": "model that contains Create POS Device Terminal response properties."}, "pbDeleteTerminalResponse": {"type": "object"}, "pbGetTerminalResponse": {"type": "object"}, "pbListTerminalsResponse": {"type": "object", "description": "model that contains List POS Device Terminals response properties."}, "pbUpdateTerminalRequest": {"type": "object", "description": "model that contains Update POS Device Terminal request properties."}, "pbUpdateTerminalResponse": {"type": "object", "description": "model that contains Update POS Device Terminal response properties."}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}