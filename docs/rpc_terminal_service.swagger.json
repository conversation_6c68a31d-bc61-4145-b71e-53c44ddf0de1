{"swagger": "2.0", "info": {"title": "Terminal Service API", "description": "Terminal Service API for authentication and POS device terminal management", "version": "1.0.0", "contact": {"name": "POS Device Terminal Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "TerminalService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/terminals/signin": {"post": {"summary": "Sign In POS Terminal Device", "description": "Authenticate POS terminal device with PCI 6 roles and also allowed target accounts", "operationId": "TerminalService_TerminalSignIn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTerminalSignInResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "model that contains POS Terminal SignIn request properties.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTerminalSignInRequest"}}], "tags": ["TerminalService"], "security": []}}}, "definitions": {"pbTerminalSignInRequest": {"type": "object", "properties": {"device_id": {"type": "string", "title": "device id"}, "tls_key": {"type": "string", "title": "tls key"}, "app_version": {"type": "string", "title": "app version"}, "firmware_version": {"type": "string", "title": "firmware version"}, "firmware_hash": {"type": "string", "title": "firmware hash"}}, "description": "model that contains POS Terminal SignIn request properties."}, "pbTerminalSignInResponse": {"type": "object", "properties": {"token": {"type": "string", "title": "generated token"}, "refresh_token": {"type": "string", "title": "refresh token"}, "expired_at": {"type": "string", "format": "int64", "title": "the expiration time of generated token"}}, "description": "model that contains POS Terminal SignIn response properties."}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}