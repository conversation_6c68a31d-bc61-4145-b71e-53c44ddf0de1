# global env configs
ENVIRONMENT=development
HEALTH_CHECK_KEY=terminal_service_health_check

# request validation json schema path
SCHEMA_PATH=../schemas
# Dapr
DAPR_HOST=localhost:50001
DAPR_SECRET_STORE_NAME=localsecretstore

# Jaeger
JAEGER_HOST=localhost:4318

# grpc listener
GRPC_LISTENER_HOST=0.0.0.0:8080

# http gateway listener
HTTP_LISTENER_HOST=0.0.0.0:8081

# authentication configs
TOKEN_SYMMETRIC_KEY=12345678901234567890123456789012
ACCESS_TOKEN_DURATION=2880
VERIFICATION_DURATION=5
REFRESH_TOKEN_DURATION=43200
ISSUER=www.liveutil.com
Audience=www.liveutil.com

# postgres configs
DB_DRIVER=postgres
DB_SOURCE=postgresql://close_loop:close_loop_postgres_pass@localhost:5433/close_loop_db?sslmode=disable
DB_MAX_POOL_SIZE=10
DB_CONNECTION_ATTEMPT=10
DB_CONNECTION_TIMEOUT=30
DB_MIGRATION_URL=file://db/migration

# redis configs
REDIS_HOST=localhost:6380
REDIS_DB=0
REDIS_PASSWORD=bBMUvLtS8KYzxuEXc0Mkj34c4Ph7eGOxMggCtictqSw0KP09

# Logs NATS connection
NATS_CONNECTION=localhost:4222

# Logging configure destination for logs
# LOG_DESTINATION=stdout, stderr, mongo, nats or blank for fmt
LOG_DESTINATION=nats
LOGS_DESTINATION_LABEL=terminal_service_LOGS

# Logs MongoDB URI
LOGS_MONGODB_URI=********************************************************************************************************************************************************************

# Notification Service Settings
NOTIFICATION_TOPIC=service.notification.v1