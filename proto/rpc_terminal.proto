syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/liveutil/terminal_service/pb";

// User message containing all user data and nested entities
message Terminal {
	// user public identifier to hide real database id of user
	string identifier = 1;
	// user approval status
	bool approved = 2;
	// user ban status
	bool banned = 3;
	// user roles
	repeated string allowed_accounts = 4;
	// allowed domains
	repeated string allowed_domains = 5;
	// allowed assets
	repeated string allowed_assets = 6;
	// user creation time
	google.protobuf.Timestamp created_at = 7;
}