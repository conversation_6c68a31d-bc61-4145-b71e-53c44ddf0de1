syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_dto.proto";

option go_package = "github.com/liveutil/terminal_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Terminal Service API";
    version: "1.0.0";
    description: "Terminal Service API for authentication and POS device terminal management";
    contact: {
      name: "POS Device Terminal Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// Terminal service provides authentication and POS terminal management endpoints
service TerminalService {
  // TerminalSignIn SignIn POS terminal device based on PCI 6 roles and also allowed target accounts.
  rpc TerminalSignIn(TerminalSignInRequest) returns (TerminalSignInResponse) {
    option (google.api.http) = {
      post: "/v1/terminals/signin"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Sign In POS Terminal Device";
      description: "Authenticate POS terminal device with PCI 6 roles and also allowed target accounts";
      security: {};
    };
  }
}