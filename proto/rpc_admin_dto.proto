syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";
import "rpc_terminal.proto";

option go_package = "github.com/liveutil/terminal_service/pb";

// model that contains Create POS Device Terminal request properties.
message CreateTerminalRequest {

}

// model that contains Create POS Device Terminal response properties.
message CreateTerminalResponse {

}

// model that contains Update POS Device Terminal request properties.
message UpdateTerminalRequest {

}

// model that contains Update POS Device Terminal response properties.
message UpdateTerminalResponse {

}

// model that contains List POS Device Terminals request properties.
message ListTerminalsRequest {
    int32 page_size = 1;
    int32 offset = 2;
}

// model that contains List POS Device Terminals response properties.
message ListTerminalsResponse {

}

// model that contains Delete POS Device Terminal request properties.
message DeleteTerminalRequest {
  string terminal_identifier = 1;
}

// 
message DeleteTerminalResponse {

}

// 
message GetTerminalRequest {
  string terminal_identifier = 1;
}

// 
message GetTerminalResponse {

}