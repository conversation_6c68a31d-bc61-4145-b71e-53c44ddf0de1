syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";
import "rpc_terminal.proto";

option go_package = "github.com/liveutil/terminal_service/pb";

// Define an empty message for methods that require no input.
message Empty {}

// model that contains POS Terminal SignIn request properties.
message TerminalSignInRequest {
  // device id
  string device_id = 1;
  // tls key
  string tls_key = 2;
  // app version 
  string app_version = 3;
  // firmware version
  string firmware_version = 4;
  // firmware hash 
  string firmware_hash = 5;
}

// model that contains POS Terminal SignIn response properties.
message TerminalSignInResponse {
  // generated token
  string token = 1;
  // refresh token 
  string refresh_token = 2;
  // the expiration time of generated token
  int64 expired_at = 3;
}