syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_dto.proto";
import "rpc_admin_dto.proto";

option go_package = "github.com/liveutil/terminal_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Terminal Service API";
    version: "1.0.0";
    description: "Terminal Service API for authentication and POS device terminal management";
    contact: {
      name: "POS Device Terminal Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// Terminal admin service provides administration related endpoints for managing POS Device Terminals.
service TerminalAdminService {
  // CreateTerminal creates a new POS Device Terminal.
  rpc CreateTerminal(CreateTerminalRequest) returns (CreateTerminalResponse) {
    option (google.api.http) = {
      post: "/v1/admin/terminals/create"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Create POS Device Terminal";
      description: "Create a new POS Device Terminal";
    };
  }

  // UpdateTerminal updates an existing POS Device Terminal.
  rpc UpdateTerminal(UpdateTerminalRequest) returns (UpdateTerminalResponse) {
    option (google.api.http) = {
      patch: "/v1/admin/terminals/update"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Update POS Device Terminal";
      description: "Update an existing POS Device Terminal";
    };
  }

  // DeleteTerminal deletes an existing POS Device Terminal.
  rpc DeleteTerminal(DeleteTerminalRequest) returns (DeleteTerminalResponse) {
    option (google.api.http) = {
      patch: "/v1/admin/terminals/delete/{terminal_identifier}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Delete POS Device Terminal";
      description: "Delete an existing POS Device Terminal";
    };
  }

  rpc GetTerminal(GetTerminalRequest) returns (GetTerminalResponse) {
    option (google.api.http) = {
      get: "/v1/admin/terminals/{terminal_identifier}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get POS Device Terminal";
      description: "Get an existing POS Device Terminal";
    };
  }

  rpc ListTerminals(ListTerminalsRequest) returns (ListTerminalsResponse) {
    option (google.api.http) = {
      get: "/v1/admin/terminals/{page_size}/{offset}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "List POS Device Terminals";
      description: "List all POS Device Terminals";
    };
  }
}