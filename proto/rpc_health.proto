syntax = "proto3";

package pb;

option go_package = "github.com/liveutil/terminal_service/pb";

// Health service provides health check endpoints for the service and required services
service Health {
  // Check health status of the service
  rpc Check(HealthCheckRequest) returns (HealthCheckResponse) {}
  // Watch health status of the service
  rpc Watch(HealthCheckRequest) returns (stream HealthCheckResponse) {}
}

// Request for checking health status of the service and required services
message HealthCheckRequest { 
  // service name to check health status for
	string service = 1;
}

// Response for checking health status of the service and required services
message HealthCheckResponse {
  // health status of the service and required services
  string status = 1;
  // timestamp of the health check
  int64 timestamp = 2;
  // details of the health status of the service and required services
  Details details = 3;
}

// Details of the health status of the service and required services
message Details {
  // health status of the database
  string database = 1;
  // health status of the redis
  string redis = 2;
  // health status of the mongodb
  string mongodb = 3;
  // health status of the message bus (NATS)
  string message_bus = 4;
  // health status of the service mesh (Dapr) or (Builtin NATS based service mesh client)
  string service_mesh = 5;
}