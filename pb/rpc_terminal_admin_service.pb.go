// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_terminal_admin_service.proto

package pb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_terminal_admin_service_proto protoreflect.FileDescriptor

const file_rpc_terminal_admin_service_proto_rawDesc = "" +
	"\n" +
	" rpc_terminal_admin_service.proto\x12\x02pb\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\x1a\rrpc_dto.proto\x1a\x13rpc_admin_dto.proto2\xba\a\n" +
	"\x14TerminalAdminService\x12\xaf\x01\n" +
	"\x0eCreateTerminal\x12\x19.pb.CreateTerminalRequest\x1a\x1a.pb.CreateTerminalResponse\"f\x92A>\x12\x1aCreate POS Device Terminal\x1a Create a new POS Device Terminal\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/v1/admin/terminals/create\x12\xb5\x01\n" +
	"\x0eUpdateTerminal\x12\x19.pb.UpdateTerminalRequest\x1a\x1a.pb.UpdateTerminalResponse\"l\x92AD\x12\x1aUpdate POS Device Terminal\x1a&Update an existing POS Device Terminal\x82\xd3\xe4\x93\x02\x1f:\x01*2\x1a/v1/admin/terminals/update\x12\xcc\x01\n" +
	"\x0eDeleteTerminal\x12\x19.pb.DeleteTerminalRequest\x1a\x1a.pb.DeleteTerminalResponse\"\x82\x01\x92AD\x12\x1aDelete POS Device Terminal\x1a&Delete an existing POS Device Terminal\x82\xd3\xe4\x93\x025:\x01*20/v1/admin/terminals/delete/{terminal_identifier}\x12\xb2\x01\n" +
	"\vGetTerminal\x12\x16.pb.GetTerminalRequest\x1a\x17.pb.GetTerminalResponse\"r\x92A>\x12\x17Get POS Device Terminal\x1a#Get an existing POS Device Terminal\x82\xd3\xe4\x93\x02+\x12)/v1/admin/terminals/{terminal_identifier}\x12\xb3\x01\n" +
	"\rListTerminals\x12\x18.pb.ListTerminalsRequest\x1a\x19.pb.ListTerminalsResponse\"m\x92A:\x12\x19List POS Device Terminals\x1a\x1dList all POS Device Terminals\x82\xd3\xe4\x93\x02*\x12(/v1/admin/terminals/{page_size}/{offset}B\xea\x02\x92A\xbd\x02\x12\xa9\x01\n" +
	"\x14Terminal Service API\x12JTerminal Service API for authentication and POS device terminal management\">\n" +
	"'POS Device Terminal Service API Support\x1a\x13liveutil@icloud.com2\x051.0.0*\x02\x01\x022\x10application/json:\x10application/jsonZY\n" +
	"W\n" +
	"\x06Bearer\x12M\b\x02\x128Authentication token, prefixed by Bearer: Bearer <token>\x1a\rAuthorization \x02b\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00Z'github.com/liveutil/terminal_service/pbb\x06proto3"

var file_rpc_terminal_admin_service_proto_goTypes = []any{
	(*CreateTerminalRequest)(nil),  // 0: pb.CreateTerminalRequest
	(*UpdateTerminalRequest)(nil),  // 1: pb.UpdateTerminalRequest
	(*DeleteTerminalRequest)(nil),  // 2: pb.DeleteTerminalRequest
	(*GetTerminalRequest)(nil),     // 3: pb.GetTerminalRequest
	(*ListTerminalsRequest)(nil),   // 4: pb.ListTerminalsRequest
	(*CreateTerminalResponse)(nil), // 5: pb.CreateTerminalResponse
	(*UpdateTerminalResponse)(nil), // 6: pb.UpdateTerminalResponse
	(*DeleteTerminalResponse)(nil), // 7: pb.DeleteTerminalResponse
	(*GetTerminalResponse)(nil),    // 8: pb.GetTerminalResponse
	(*ListTerminalsResponse)(nil),  // 9: pb.ListTerminalsResponse
}
var file_rpc_terminal_admin_service_proto_depIdxs = []int32{
	0, // 0: pb.TerminalAdminService.CreateTerminal:input_type -> pb.CreateTerminalRequest
	1, // 1: pb.TerminalAdminService.UpdateTerminal:input_type -> pb.UpdateTerminalRequest
	2, // 2: pb.TerminalAdminService.DeleteTerminal:input_type -> pb.DeleteTerminalRequest
	3, // 3: pb.TerminalAdminService.GetTerminal:input_type -> pb.GetTerminalRequest
	4, // 4: pb.TerminalAdminService.ListTerminals:input_type -> pb.ListTerminalsRequest
	5, // 5: pb.TerminalAdminService.CreateTerminal:output_type -> pb.CreateTerminalResponse
	6, // 6: pb.TerminalAdminService.UpdateTerminal:output_type -> pb.UpdateTerminalResponse
	7, // 7: pb.TerminalAdminService.DeleteTerminal:output_type -> pb.DeleteTerminalResponse
	8, // 8: pb.TerminalAdminService.GetTerminal:output_type -> pb.GetTerminalResponse
	9, // 9: pb.TerminalAdminService.ListTerminals:output_type -> pb.ListTerminalsResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_terminal_admin_service_proto_init() }
func file_rpc_terminal_admin_service_proto_init() {
	if File_rpc_terminal_admin_service_proto != nil {
		return
	}
	file_rpc_dto_proto_init()
	file_rpc_admin_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_terminal_admin_service_proto_rawDesc), len(file_rpc_terminal_admin_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_terminal_admin_service_proto_goTypes,
		DependencyIndexes: file_rpc_terminal_admin_service_proto_depIdxs,
	}.Build()
	File_rpc_terminal_admin_service_proto = out.File
	file_rpc_terminal_admin_service_proto_goTypes = nil
	file_rpc_terminal_admin_service_proto_depIdxs = nil
}
