// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Define an empty message for methods that require no input.
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_rpc_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{0}
}

// model that contains POS Terminal SignIn request properties.
type TerminalSignInRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// device id
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// tls key
	TlsKey string `protobuf:"bytes,2,opt,name=tls_key,json=tlsKey,proto3" json:"tls_key,omitempty"`
	// app version
	AppVersion string `protobuf:"bytes,3,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// firmware version
	FirmwareVersion string `protobuf:"bytes,4,opt,name=firmware_version,json=firmwareVersion,proto3" json:"firmware_version,omitempty"`
	// firmware hash
	FirmwareHash  string `protobuf:"bytes,5,opt,name=firmware_hash,json=firmwareHash,proto3" json:"firmware_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TerminalSignInRequest) Reset() {
	*x = TerminalSignInRequest{}
	mi := &file_rpc_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalSignInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalSignInRequest) ProtoMessage() {}

func (x *TerminalSignInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalSignInRequest.ProtoReflect.Descriptor instead.
func (*TerminalSignInRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{1}
}

func (x *TerminalSignInRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *TerminalSignInRequest) GetTlsKey() string {
	if x != nil {
		return x.TlsKey
	}
	return ""
}

func (x *TerminalSignInRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *TerminalSignInRequest) GetFirmwareVersion() string {
	if x != nil {
		return x.FirmwareVersion
	}
	return ""
}

func (x *TerminalSignInRequest) GetFirmwareHash() string {
	if x != nil {
		return x.FirmwareHash
	}
	return ""
}

// model that contains POS Terminal SignIn response properties.
type TerminalSignInResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// generated token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// refresh token
	RefreshToken string `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	// the expiration time of generated token
	ExpiredAt     int64 `protobuf:"varint,3,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TerminalSignInResponse) Reset() {
	*x = TerminalSignInResponse{}
	mi := &file_rpc_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalSignInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalSignInResponse) ProtoMessage() {}

func (x *TerminalSignInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalSignInResponse.ProtoReflect.Descriptor instead.
func (*TerminalSignInResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{2}
}

func (x *TerminalSignInResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TerminalSignInResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *TerminalSignInResponse) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

var File_rpc_dto_proto protoreflect.FileDescriptor

const file_rpc_dto_proto_rawDesc = "" +
	"\n" +
	"\rrpc_dto.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x12rpc_terminal.proto\"\a\n" +
	"\x05Empty\"\xbe\x01\n" +
	"\x15TerminalSignInRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x17\n" +
	"\atls_key\x18\x02 \x01(\tR\x06tlsKey\x12\x1f\n" +
	"\vapp_version\x18\x03 \x01(\tR\n" +
	"appVersion\x12)\n" +
	"\x10firmware_version\x18\x04 \x01(\tR\x0ffirmwareVersion\x12#\n" +
	"\rfirmware_hash\x18\x05 \x01(\tR\ffirmwareHash\"r\n" +
	"\x16TerminalSignInResponse\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x02 \x01(\tR\frefreshToken\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x03 \x01(\x03R\texpiredAtB)Z'github.com/liveutil/terminal_service/pbb\x06proto3"

var (
	file_rpc_dto_proto_rawDescOnce sync.Once
	file_rpc_dto_proto_rawDescData []byte
)

func file_rpc_dto_proto_rawDescGZIP() []byte {
	file_rpc_dto_proto_rawDescOnce.Do(func() {
		file_rpc_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)))
	})
	return file_rpc_dto_proto_rawDescData
}

var file_rpc_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_rpc_dto_proto_goTypes = []any{
	(*Empty)(nil),                  // 0: pb.Empty
	(*TerminalSignInRequest)(nil),  // 1: pb.TerminalSignInRequest
	(*TerminalSignInResponse)(nil), // 2: pb.TerminalSignInResponse
}
var file_rpc_dto_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_dto_proto_init() }
func file_rpc_dto_proto_init() {
	if File_rpc_dto_proto != nil {
		return
	}
	file_rpc_terminal_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_dto_proto_goTypes,
		DependencyIndexes: file_rpc_dto_proto_depIdxs,
		MessageInfos:      file_rpc_dto_proto_msgTypes,
	}.Build()
	File_rpc_dto_proto = out.File
	file_rpc_dto_proto_goTypes = nil
	file_rpc_dto_proto_depIdxs = nil
}
