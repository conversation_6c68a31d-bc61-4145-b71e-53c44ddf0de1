// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: rpc_terminal_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TerminalService_TerminalSignIn_FullMethodName = "/pb.TerminalService/TerminalSignIn"
)

// TerminalServiceClient is the client API for TerminalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Terminal service provides authentication and POS terminal management endpoints
type TerminalServiceClient interface {
	// TerminalSignIn SignIn POS terminal device based on PCI 6 roles and also allowed target accounts.
	TerminalSignIn(ctx context.Context, in *TerminalSignInRequest, opts ...grpc.CallOption) (*TerminalSignInResponse, error)
}

type terminalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTerminalServiceClient(cc grpc.ClientConnInterface) TerminalServiceClient {
	return &terminalServiceClient{cc}
}

func (c *terminalServiceClient) TerminalSignIn(ctx context.Context, in *TerminalSignInRequest, opts ...grpc.CallOption) (*TerminalSignInResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TerminalSignInResponse)
	err := c.cc.Invoke(ctx, TerminalService_TerminalSignIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TerminalServiceServer is the server API for TerminalService service.
// All implementations must embed UnimplementedTerminalServiceServer
// for forward compatibility.
//
// Terminal service provides authentication and POS terminal management endpoints
type TerminalServiceServer interface {
	// TerminalSignIn SignIn POS terminal device based on PCI 6 roles and also allowed target accounts.
	TerminalSignIn(context.Context, *TerminalSignInRequest) (*TerminalSignInResponse, error)
	mustEmbedUnimplementedTerminalServiceServer()
}

// UnimplementedTerminalServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTerminalServiceServer struct{}

func (UnimplementedTerminalServiceServer) TerminalSignIn(context.Context, *TerminalSignInRequest) (*TerminalSignInResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TerminalSignIn not implemented")
}
func (UnimplementedTerminalServiceServer) mustEmbedUnimplementedTerminalServiceServer() {}
func (UnimplementedTerminalServiceServer) testEmbeddedByValue()                         {}

// UnsafeTerminalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TerminalServiceServer will
// result in compilation errors.
type UnsafeTerminalServiceServer interface {
	mustEmbedUnimplementedTerminalServiceServer()
}

func RegisterTerminalServiceServer(s grpc.ServiceRegistrar, srv TerminalServiceServer) {
	// If the following call pancis, it indicates UnimplementedTerminalServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TerminalService_ServiceDesc, srv)
}

func _TerminalService_TerminalSignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TerminalSignInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalServiceServer).TerminalSignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TerminalService_TerminalSignIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalServiceServer).TerminalSignIn(ctx, req.(*TerminalSignInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TerminalService_ServiceDesc is the grpc.ServiceDesc for TerminalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TerminalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TerminalService",
	HandlerType: (*TerminalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TerminalSignIn",
			Handler:    _TerminalService_TerminalSignIn_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc_terminal_service.proto",
}
