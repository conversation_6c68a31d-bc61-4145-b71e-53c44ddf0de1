// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: rpc_terminal_admin_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TerminalAdminService_CreateTerminal_FullMethodName = "/pb.TerminalAdminService/CreateTerminal"
	TerminalAdminService_UpdateTerminal_FullMethodName = "/pb.TerminalAdminService/UpdateTerminal"
	TerminalAdminService_DeleteTerminal_FullMethodName = "/pb.TerminalAdminService/DeleteTerminal"
	TerminalAdminService_GetTerminal_FullMethodName    = "/pb.TerminalAdminService/GetTerminal"
	TerminalAdminService_ListTerminals_FullMethodName  = "/pb.TerminalAdminService/ListTerminals"
)

// TerminalAdminServiceClient is the client API for TerminalAdminService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Terminal admin service provides administration related endpoints for managing POS Device Terminals.
type TerminalAdminServiceClient interface {
	// CreateTerminal creates a new POS Device Terminal.
	CreateTerminal(ctx context.Context, in *CreateTerminalRequest, opts ...grpc.CallOption) (*CreateTerminalResponse, error)
	// UpdateTerminal updates an existing POS Device Terminal.
	UpdateTerminal(ctx context.Context, in *UpdateTerminalRequest, opts ...grpc.CallOption) (*UpdateTerminalResponse, error)
	// DeleteTerminal deletes an existing POS Device Terminal.
	DeleteTerminal(ctx context.Context, in *DeleteTerminalRequest, opts ...grpc.CallOption) (*DeleteTerminalResponse, error)
	GetTerminal(ctx context.Context, in *GetTerminalRequest, opts ...grpc.CallOption) (*GetTerminalResponse, error)
	ListTerminals(ctx context.Context, in *ListTerminalsRequest, opts ...grpc.CallOption) (*ListTerminalsResponse, error)
}

type terminalAdminServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTerminalAdminServiceClient(cc grpc.ClientConnInterface) TerminalAdminServiceClient {
	return &terminalAdminServiceClient{cc}
}

func (c *terminalAdminServiceClient) CreateTerminal(ctx context.Context, in *CreateTerminalRequest, opts ...grpc.CallOption) (*CreateTerminalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTerminalResponse)
	err := c.cc.Invoke(ctx, TerminalAdminService_CreateTerminal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalAdminServiceClient) UpdateTerminal(ctx context.Context, in *UpdateTerminalRequest, opts ...grpc.CallOption) (*UpdateTerminalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateTerminalResponse)
	err := c.cc.Invoke(ctx, TerminalAdminService_UpdateTerminal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalAdminServiceClient) DeleteTerminal(ctx context.Context, in *DeleteTerminalRequest, opts ...grpc.CallOption) (*DeleteTerminalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteTerminalResponse)
	err := c.cc.Invoke(ctx, TerminalAdminService_DeleteTerminal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalAdminServiceClient) GetTerminal(ctx context.Context, in *GetTerminalRequest, opts ...grpc.CallOption) (*GetTerminalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTerminalResponse)
	err := c.cc.Invoke(ctx, TerminalAdminService_GetTerminal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *terminalAdminServiceClient) ListTerminals(ctx context.Context, in *ListTerminalsRequest, opts ...grpc.CallOption) (*ListTerminalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTerminalsResponse)
	err := c.cc.Invoke(ctx, TerminalAdminService_ListTerminals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TerminalAdminServiceServer is the server API for TerminalAdminService service.
// All implementations must embed UnimplementedTerminalAdminServiceServer
// for forward compatibility.
//
// Terminal admin service provides administration related endpoints for managing POS Device Terminals.
type TerminalAdminServiceServer interface {
	// CreateTerminal creates a new POS Device Terminal.
	CreateTerminal(context.Context, *CreateTerminalRequest) (*CreateTerminalResponse, error)
	// UpdateTerminal updates an existing POS Device Terminal.
	UpdateTerminal(context.Context, *UpdateTerminalRequest) (*UpdateTerminalResponse, error)
	// DeleteTerminal deletes an existing POS Device Terminal.
	DeleteTerminal(context.Context, *DeleteTerminalRequest) (*DeleteTerminalResponse, error)
	GetTerminal(context.Context, *GetTerminalRequest) (*GetTerminalResponse, error)
	ListTerminals(context.Context, *ListTerminalsRequest) (*ListTerminalsResponse, error)
	mustEmbedUnimplementedTerminalAdminServiceServer()
}

// UnimplementedTerminalAdminServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTerminalAdminServiceServer struct{}

func (UnimplementedTerminalAdminServiceServer) CreateTerminal(context.Context, *CreateTerminalRequest) (*CreateTerminalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTerminal not implemented")
}
func (UnimplementedTerminalAdminServiceServer) UpdateTerminal(context.Context, *UpdateTerminalRequest) (*UpdateTerminalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTerminal not implemented")
}
func (UnimplementedTerminalAdminServiceServer) DeleteTerminal(context.Context, *DeleteTerminalRequest) (*DeleteTerminalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTerminal not implemented")
}
func (UnimplementedTerminalAdminServiceServer) GetTerminal(context.Context, *GetTerminalRequest) (*GetTerminalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerminal not implemented")
}
func (UnimplementedTerminalAdminServiceServer) ListTerminals(context.Context, *ListTerminalsRequest) (*ListTerminalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTerminals not implemented")
}
func (UnimplementedTerminalAdminServiceServer) mustEmbedUnimplementedTerminalAdminServiceServer() {}
func (UnimplementedTerminalAdminServiceServer) testEmbeddedByValue()                              {}

// UnsafeTerminalAdminServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TerminalAdminServiceServer will
// result in compilation errors.
type UnsafeTerminalAdminServiceServer interface {
	mustEmbedUnimplementedTerminalAdminServiceServer()
}

func RegisterTerminalAdminServiceServer(s grpc.ServiceRegistrar, srv TerminalAdminServiceServer) {
	// If the following call pancis, it indicates UnimplementedTerminalAdminServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TerminalAdminService_ServiceDesc, srv)
}

func _TerminalAdminService_CreateTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTerminalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalAdminServiceServer).CreateTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TerminalAdminService_CreateTerminal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalAdminServiceServer).CreateTerminal(ctx, req.(*CreateTerminalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerminalAdminService_UpdateTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTerminalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalAdminServiceServer).UpdateTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TerminalAdminService_UpdateTerminal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalAdminServiceServer).UpdateTerminal(ctx, req.(*UpdateTerminalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerminalAdminService_DeleteTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTerminalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalAdminServiceServer).DeleteTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TerminalAdminService_DeleteTerminal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalAdminServiceServer).DeleteTerminal(ctx, req.(*DeleteTerminalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerminalAdminService_GetTerminal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTerminalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalAdminServiceServer).GetTerminal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TerminalAdminService_GetTerminal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalAdminServiceServer).GetTerminal(ctx, req.(*GetTerminalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TerminalAdminService_ListTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTerminalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TerminalAdminServiceServer).ListTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TerminalAdminService_ListTerminals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TerminalAdminServiceServer).ListTerminals(ctx, req.(*ListTerminalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TerminalAdminService_ServiceDesc is the grpc.ServiceDesc for TerminalAdminService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TerminalAdminService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TerminalAdminService",
	HandlerType: (*TerminalAdminServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTerminal",
			Handler:    _TerminalAdminService_CreateTerminal_Handler,
		},
		{
			MethodName: "UpdateTerminal",
			Handler:    _TerminalAdminService_UpdateTerminal_Handler,
		},
		{
			MethodName: "DeleteTerminal",
			Handler:    _TerminalAdminService_DeleteTerminal_Handler,
		},
		{
			MethodName: "GetTerminal",
			Handler:    _TerminalAdminService_GetTerminal_Handler,
		},
		{
			MethodName: "ListTerminals",
			Handler:    _TerminalAdminService_ListTerminals_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc_terminal_admin_service.proto",
}
