// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_terminal.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// User message containing all user data and nested entities
type Terminal struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// user public identifier to hide real database id of user
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// user approval status
	Approved bool `protobuf:"varint,2,opt,name=approved,proto3" json:"approved,omitempty"`
	// user ban status
	Banned bool `protobuf:"varint,3,opt,name=banned,proto3" json:"banned,omitempty"`
	// user roles
	AllowedAccounts []string `protobuf:"bytes,4,rep,name=allowed_accounts,json=allowedAccounts,proto3" json:"allowed_accounts,omitempty"`
	// allowed domains
	AllowedDomains []string `protobuf:"bytes,5,rep,name=allowed_domains,json=allowedDomains,proto3" json:"allowed_domains,omitempty"`
	// allowed assets
	AllowedAssets []string `protobuf:"bytes,6,rep,name=allowed_assets,json=allowedAssets,proto3" json:"allowed_assets,omitempty"`
	// user creation time
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Terminal) Reset() {
	*x = Terminal{}
	mi := &file_rpc_terminal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Terminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Terminal) ProtoMessage() {}

func (x *Terminal) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_terminal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Terminal.ProtoReflect.Descriptor instead.
func (*Terminal) Descriptor() ([]byte, []int) {
	return file_rpc_terminal_proto_rawDescGZIP(), []int{0}
}

func (x *Terminal) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *Terminal) GetApproved() bool {
	if x != nil {
		return x.Approved
	}
	return false
}

func (x *Terminal) GetBanned() bool {
	if x != nil {
		return x.Banned
	}
	return false
}

func (x *Terminal) GetAllowedAccounts() []string {
	if x != nil {
		return x.AllowedAccounts
	}
	return nil
}

func (x *Terminal) GetAllowedDomains() []string {
	if x != nil {
		return x.AllowedDomains
	}
	return nil
}

func (x *Terminal) GetAllowedAssets() []string {
	if x != nil {
		return x.AllowedAssets
	}
	return nil
}

func (x *Terminal) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_rpc_terminal_proto protoreflect.FileDescriptor

const file_rpc_terminal_proto_rawDesc = "" +
	"\n" +
	"\x12rpc_terminal.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\"\x94\x02\n" +
	"\bTerminal\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x1a\n" +
	"\bapproved\x18\x02 \x01(\bR\bapproved\x12\x16\n" +
	"\x06banned\x18\x03 \x01(\bR\x06banned\x12)\n" +
	"\x10allowed_accounts\x18\x04 \x03(\tR\x0fallowedAccounts\x12'\n" +
	"\x0fallowed_domains\x18\x05 \x03(\tR\x0eallowedDomains\x12%\n" +
	"\x0eallowed_assets\x18\x06 \x03(\tR\rallowedAssets\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAtB)Z'github.com/liveutil/terminal_service/pbb\x06proto3"

var (
	file_rpc_terminal_proto_rawDescOnce sync.Once
	file_rpc_terminal_proto_rawDescData []byte
)

func file_rpc_terminal_proto_rawDescGZIP() []byte {
	file_rpc_terminal_proto_rawDescOnce.Do(func() {
		file_rpc_terminal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_terminal_proto_rawDesc), len(file_rpc_terminal_proto_rawDesc)))
	})
	return file_rpc_terminal_proto_rawDescData
}

var file_rpc_terminal_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_rpc_terminal_proto_goTypes = []any{
	(*Terminal)(nil),              // 0: pb.Terminal
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_rpc_terminal_proto_depIdxs = []int32{
	1, // 0: pb.Terminal.created_at:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rpc_terminal_proto_init() }
func file_rpc_terminal_proto_init() {
	if File_rpc_terminal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_terminal_proto_rawDesc), len(file_rpc_terminal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_terminal_proto_goTypes,
		DependencyIndexes: file_rpc_terminal_proto_depIdxs,
		MessageInfos:      file_rpc_terminal_proto_msgTypes,
	}.Build()
	File_rpc_terminal_proto = out.File
	file_rpc_terminal_proto_goTypes = nil
	file_rpc_terminal_proto_depIdxs = nil
}
