// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_admin_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// model that contains Create POS Device Terminal request properties.
type CreateTerminalRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTerminalRequest) Reset() {
	*x = CreateTerminalRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTerminalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTerminalRequest) ProtoMessage() {}

func (x *CreateTerminalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTerminalRequest.ProtoReflect.Descriptor instead.
func (*CreateTerminalRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{0}
}

// model that contains Create POS Device Terminal response properties.
type CreateTerminalResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTerminalResponse) Reset() {
	*x = CreateTerminalResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTerminalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTerminalResponse) ProtoMessage() {}

func (x *CreateTerminalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTerminalResponse.ProtoReflect.Descriptor instead.
func (*CreateTerminalResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{1}
}

// model that contains Update POS Device Terminal request properties.
type UpdateTerminalRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTerminalRequest) Reset() {
	*x = UpdateTerminalRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTerminalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTerminalRequest) ProtoMessage() {}

func (x *UpdateTerminalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTerminalRequest.ProtoReflect.Descriptor instead.
func (*UpdateTerminalRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{2}
}

// model that contains Update POS Device Terminal response properties.
type UpdateTerminalResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTerminalResponse) Reset() {
	*x = UpdateTerminalResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTerminalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTerminalResponse) ProtoMessage() {}

func (x *UpdateTerminalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTerminalResponse.ProtoReflect.Descriptor instead.
func (*UpdateTerminalResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{3}
}

// model that contains List POS Device Terminals request properties.
type ListTerminalsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageSize      int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Offset        int32                  `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTerminalsRequest) Reset() {
	*x = ListTerminalsRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTerminalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTerminalsRequest) ProtoMessage() {}

func (x *ListTerminalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTerminalsRequest.ProtoReflect.Descriptor instead.
func (*ListTerminalsRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{4}
}

func (x *ListTerminalsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTerminalsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// model that contains List POS Device Terminals response properties.
type ListTerminalsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTerminalsResponse) Reset() {
	*x = ListTerminalsResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTerminalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTerminalsResponse) ProtoMessage() {}

func (x *ListTerminalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTerminalsResponse.ProtoReflect.Descriptor instead.
func (*ListTerminalsResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{5}
}

// model that contains Delete POS Device Terminal request properties.
type DeleteTerminalRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TerminalIdentifier string                 `protobuf:"bytes,1,opt,name=terminal_identifier,json=terminalIdentifier,proto3" json:"terminal_identifier,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DeleteTerminalRequest) Reset() {
	*x = DeleteTerminalRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTerminalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTerminalRequest) ProtoMessage() {}

func (x *DeleteTerminalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTerminalRequest.ProtoReflect.Descriptor instead.
func (*DeleteTerminalRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteTerminalRequest) GetTerminalIdentifier() string {
	if x != nil {
		return x.TerminalIdentifier
	}
	return ""
}

type DeleteTerminalResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTerminalResponse) Reset() {
	*x = DeleteTerminalResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTerminalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTerminalResponse) ProtoMessage() {}

func (x *DeleteTerminalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTerminalResponse.ProtoReflect.Descriptor instead.
func (*DeleteTerminalResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{7}
}

type GetTerminalRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TerminalIdentifier string                 `protobuf:"bytes,1,opt,name=terminal_identifier,json=terminalIdentifier,proto3" json:"terminal_identifier,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetTerminalRequest) Reset() {
	*x = GetTerminalRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTerminalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalRequest) ProtoMessage() {}

func (x *GetTerminalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalRequest.ProtoReflect.Descriptor instead.
func (*GetTerminalRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{8}
}

func (x *GetTerminalRequest) GetTerminalIdentifier() string {
	if x != nil {
		return x.TerminalIdentifier
	}
	return ""
}

type GetTerminalResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTerminalResponse) Reset() {
	*x = GetTerminalResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTerminalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalResponse) ProtoMessage() {}

func (x *GetTerminalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalResponse.ProtoReflect.Descriptor instead.
func (*GetTerminalResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{9}
}

var File_rpc_admin_dto_proto protoreflect.FileDescriptor

const file_rpc_admin_dto_proto_rawDesc = "" +
	"\n" +
	"\x13rpc_admin_dto.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x12rpc_terminal.proto\"\x17\n" +
	"\x15CreateTerminalRequest\"\x18\n" +
	"\x16CreateTerminalResponse\"\x17\n" +
	"\x15UpdateTerminalRequest\"\x18\n" +
	"\x16UpdateTerminalResponse\"K\n" +
	"\x14ListTerminalsRequest\x12\x1b\n" +
	"\tpage_size\x18\x01 \x01(\x05R\bpageSize\x12\x16\n" +
	"\x06offset\x18\x02 \x01(\x05R\x06offset\"\x17\n" +
	"\x15ListTerminalsResponse\"H\n" +
	"\x15DeleteTerminalRequest\x12/\n" +
	"\x13terminal_identifier\x18\x01 \x01(\tR\x12terminalIdentifier\"\x18\n" +
	"\x16DeleteTerminalResponse\"E\n" +
	"\x12GetTerminalRequest\x12/\n" +
	"\x13terminal_identifier\x18\x01 \x01(\tR\x12terminalIdentifier\"\x15\n" +
	"\x13GetTerminalResponseB)Z'github.com/liveutil/terminal_service/pbb\x06proto3"

var (
	file_rpc_admin_dto_proto_rawDescOnce sync.Once
	file_rpc_admin_dto_proto_rawDescData []byte
)

func file_rpc_admin_dto_proto_rawDescGZIP() []byte {
	file_rpc_admin_dto_proto_rawDescOnce.Do(func() {
		file_rpc_admin_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_admin_dto_proto_rawDesc), len(file_rpc_admin_dto_proto_rawDesc)))
	})
	return file_rpc_admin_dto_proto_rawDescData
}

var file_rpc_admin_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_rpc_admin_dto_proto_goTypes = []any{
	(*CreateTerminalRequest)(nil),  // 0: pb.CreateTerminalRequest
	(*CreateTerminalResponse)(nil), // 1: pb.CreateTerminalResponse
	(*UpdateTerminalRequest)(nil),  // 2: pb.UpdateTerminalRequest
	(*UpdateTerminalResponse)(nil), // 3: pb.UpdateTerminalResponse
	(*ListTerminalsRequest)(nil),   // 4: pb.ListTerminalsRequest
	(*ListTerminalsResponse)(nil),  // 5: pb.ListTerminalsResponse
	(*DeleteTerminalRequest)(nil),  // 6: pb.DeleteTerminalRequest
	(*DeleteTerminalResponse)(nil), // 7: pb.DeleteTerminalResponse
	(*GetTerminalRequest)(nil),     // 8: pb.GetTerminalRequest
	(*GetTerminalResponse)(nil),    // 9: pb.GetTerminalResponse
}
var file_rpc_admin_dto_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_admin_dto_proto_init() }
func file_rpc_admin_dto_proto_init() {
	if File_rpc_admin_dto_proto != nil {
		return
	}
	file_rpc_terminal_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_admin_dto_proto_rawDesc), len(file_rpc_admin_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_admin_dto_proto_goTypes,
		DependencyIndexes: file_rpc_admin_dto_proto_depIdxs,
		MessageInfos:      file_rpc_admin_dto_proto_msgTypes,
	}.Build()
	File_rpc_admin_dto_proto = out.File
	file_rpc_admin_dto_proto_goTypes = nil
	file_rpc_admin_dto_proto_depIdxs = nil
}
